<!DOCTYPE html>
<html>
<head>
    <title>Meme Content Generator</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/meme_workshop">🎭 Meme Workshop</a>
            <a href="/audio_workshop">🎵 Audio Workshop</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <!-- Flash Messages -->
        {% if session.flash_message and ('dashboard' in session.flash_context or not session.flash_context) %}
        <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
            {{ session.flash_message }}
            {% set _ = session.pop('flash_message') %}
            {% set _ = session.pop('flash_context') %}
        </div>
        {% endif %}

        <!-- Enhanced Dashboard Header -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">🎭 Meme Video Generator</h1>
            <p class="dashboard-subtitle">Your complete content creation hub - from memes to viral videos</p>
        </div>

        <!-- Workflow Statistics -->
        <h2>🔄 Workflow Progress</h2>
        <div class="stats-grid">
            <div class="stat-card info">
                <div class="stat-number">{{ stats.total_memes }}</div>
                <div class="stat-label">Total Memes</div>
                <div class="stat-description">Fetched from Reddit</div>
            </div>

            <div class="stat-card warning">
                <div class="stat-number">{{ stats.memes_pending_text }}</div>
                <div class="stat-label">Pending Text Review</div>
                <div class="stat-description">Awaiting text addition & approval</div>
            </div>

            <div class="stat-card success">
                <div class="stat-number">{{ stats.memes_text_approved }}</div>
                <div class="stat-label">Text Approved</div>
                <div class="stat-description">Ready for audio phase</div>
            </div>

            <div class="stat-card warning">
                <div class="stat-number">{{ stats.memes_pending_audio }}</div>
                <div class="stat-label">Pending Audio Review</div>
                <div class="stat-description">Text memes awaiting audio approval</div>
            </div>

            <div class="stat-card success">
                <div class="stat-number">{{ stats.memes_audio_approved }}</div>
                <div class="stat-label">Audio Approved</div>
                <div class="stat-description">Ready for content generation</div>
            </div>
        </div>

        <!-- Content Type Breakdown -->
        <h2>📊 Content Types</h2>
        <div class="stats-grid">
            <div class="stat-card info">
                <div class="stat-number">{{ stats.memes_without_text }}</div>
                <div class="stat-label">Image-Only Content</div>
                <div class="stat-description">Memes without text</div>
            </div>

            <div class="stat-card success">
                <div class="stat-number">{{ stats.memes_with_text }}</div>
                <div class="stat-label">Audio Content</div>
                <div class="stat-description">Memes with text and narration</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">{{ stats.memes_today }}</div>
                <div class="stat-label">Memes Today</div>
                <div class="stat-description">Fetched today</div>
            </div>
        </div>



        <!-- Subreddit Breakdown -->
        {% if stats.subreddit_stats %}
        <h2>📂 Subreddit Progress</h2>
        <div class="subreddit-stats">
            {% for subreddit, data in stats.subreddit_stats.items() %}
            <div class="subreddit-card">
                <h3>r/{{ subreddit }}</h3>
                <div class="subreddit-progress">
                    <div class="progress-step">
                        <span class="step-number">{{ data.total }}</span>
                        <span class="step-label">Fetched</span>
                    </div>
                    <div class="progress-arrow">→</div>
                    <div class="progress-step">
                        <span class="step-number">{{ data.text_approved }}</span>
                        <span class="step-label">Text Approved</span>
                    </div>
                    <div class="progress-arrow">→</div>
                    <div class="progress-step">
                        <span class="step-number">{{ data.audio_approved }}</span>
                        <span class="step-label">Audio Approved</span>
                    </div>
                </div>
                <div class="subreddit-percentage">
                    {{ ((data.audio_approved / data.total) * 100) | round(1) if data.total > 0 else 0 }}% Complete
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Overall Workflow Status -->
        {% if stats.total_memes > 0 %}
        <h2>📈 Overall Pipeline Status</h2>
        <div class="workflow-progress">
            <div class="workflow-header">
                <span class="workflow-title">Content Pipeline Progress</span>
                <span class="workflow-percentage">{{ ((stats.memes_audio_approved / stats.total_memes) * 100) | round(1) if stats.total_memes > 0 else 0 }}% Ready</span>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ ((stats.memes_audio_approved / stats.total_memes) * 100) if stats.total_memes > 0 else 0 }}%;"></div>
                </div>
            </div>
            <div class="workflow-details">
                {{ stats.total_memes }} memes → {{ stats.memes_audio_approved }} ready for content generation ({{ stats.memes_with_text }} with audio, {{ stats.memes_without_text }} image-only)
            </div>
        </div>
        {% endif %}

        <!-- Additional Stats -->
        {% if stats.discarded_memes > 0 %}
        <h2>🗑️ Discarded Content</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ stats.discarded_memes }}</div>
                <div class="stat-label">Discarded Memes</div>
                <div class="stat-description">Removed from workflow</div>
            </div>
        </div>
        {% endif %}

        <!-- Admin Actions -->
        <h2>🔧 Admin Actions</h2>
        <div class="admin-actions">
            <form method="POST" action="/clear_all_data" style="display: inline;">
                <button type="submit" class="btn btn-danger"
                        onclick="return confirm('⚠️ WARNING: This will delete ALL data except user accounts. This cannot be undone. Are you absolutely sure?')">
                    🗑️ Clear All Data
                </button>
            </form>
        </div>
    </div>


</body>
</html>
